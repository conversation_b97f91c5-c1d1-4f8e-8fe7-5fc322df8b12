<div *ngIf="permissions?.has('Permissions.Leads.View') else leadsLoader" [formGroup]="filtersForm">
    <div class="bg-white w-100 px-24 py-6" [ngClass]="showLeftNav ? 'left-230' : 'left-125'">
        <div class="flex-between flex-grow-1">
            <ul
                class="ph-d-none tb-mt-10 align-center top-nav-bar text-nowrap scrollbar ip-w-100-190 tb-w-100-280 w-100-430 scroll-hide">
                <ng-container *ngFor="let visibilityImage of visibilityList; let i = index">
                    <ng-container *ngIf="!(visibilityImage.name === 'Unassigned' && currentPath === '/invoice')">
                        <div class="cursor-pointer" (click)="currentVisibility(visibilityImage.name)">
                            <div class="align-center ph-mb-4">
                                <a
                                    [class.active]="getFormValue('leadVisibility') == LeadVisibilityEnum[visibilityImage.name]">
                                    <img [type]="'leadrat'" [appImage]="s3BucketUrl + visibilityImage.image" alt="logo"
                                        width="20" height="20"></a>
                                <span
                                    [class.active]="getFormValue('leadVisibility') == LeadVisibilityEnum[visibilityImage.name]"
                                    class="text-large fw-semi-bold mx-8 d-flex">{{ visibilityImage.name }}
                                    <ng-container *ngIf="!baseFilterCountIsLoading; else statusLoader">
                                        ({{baseFilterCount?.[visibilityImage?.count] || 0 }})
                                    </ng-container>
                                </span>
                            </div>
                        </div>
                    </ng-container>
                </ng-container>
                <div *ngIf="isShowParentLead">
                    <label for="showParentLead" class="text-large fw-semi-bold mx-8 d-flex checkbox-container">Show
                        Parent Lead
                        <input (change)="filterFunction();" formControlName="shouldShowParentLead" type="checkbox"
                            name="" id="showParentLead">
                        <span class="checkmark bg-white"></span>
                    </label>
                </div>
            </ul>
            <div class="ph-d-flex ng-select-sm" [ngClass]="{ 'd-none': !isDropdownVisible }">
                <ng-select [items]="visibilityList" [(ngModel)]="currentVisibilityName" ResizableDropdown
                    [ngModelOptions]="{standalone: true}" bindLabel="name" bindValue="name"
                    (change)="currentVisibility($event?.name)" [clearable]="false" placeholder="Select Visibility"
                    class="w-170" form>
                    <ng-template ng-label-tmp let-item="item">
                        <img [type]="'leadrat'" [appImage]="s3BucketUrl + item?.image" [type]="'orgProfileLogo'"
                            alt="logo" height="15px" width="30px">
                        <span class="mx-2">{{ item.name }} <span
                                *ngIf="!baseFilterCountIsLoading; else statusLoader">({{
                                baseFilterCount?.[item?.count] || 0 }})</span> </span>
                    </ng-template>

                    <ng-template ng-option-tmp let-item="item" let-index="index">
                        <img [type]="'leadrat'" [appImage]="s3BucketUrl + item?.image" [type]="'orgProfileLogo'"
                            alt="icon" height="15px" width="30px">
                        {{ item.name }} <span *ngIf="!baseFilterCountIsLoading; else statusLoader">({{
                            baseFilterCount?.[item?.count] || 0 }})</span>
                    </ng-template>
                </ng-select>
                <div *ngIf="isShowParentLead" class="mt-2">
                    <label for="showParentLead" class="checkbox-container">
                        <span class="text-large fw-semi-bold mx-8">Show Parent Lead</span>
                        <input type="checkbox" id="showParentLead" formControlName="shouldShowParentLead"
                            (change)="filterFunction();">
                        <span class="checkmark bg-white"></span>
                    </label>
                </div>
            </div>
            <div class="flex-end mr-4">
                <div class="btn-coal mr-8">
                    <span class="ic-tracker icon ic-xxs"></span>
                    <span>Auto Dialer</span>
                </div>
                <ng-container
                    *ngIf="permissions?.has('Permissions.Leads.BulkUpload') || permissions?.has('Permissions.Leads.Export')">
                    <div class="btn-full-dropdown btn-w-100">
                        <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
                            <span class="ic-tracker icon ic-xxs"></span>
                            <span class="ml-8 ip-d-none">Tracker</span>
                        </div>
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                            [(ngModel)]="selectedTrackerOption" [ngModelOptions]="{standalone: true}" ResizableDropdown
                            (click)="openLeadsTracker()">
                            <ng-option (click)="selectedTrackerOption = null" value="bulkUpload"
                                *ngIf="currentPath === '/invoice' ? false : permissions?.has('Permissions.Leads.BulkUpload')">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
                            <ng-option (click)="selectedTrackerOption = null" value="export"
                                *ngIf="permissions?.has('Permissions.Leads.Export')">
                                <span class="ic-download icon ic-xxs ic-dark mr-8"></span>Export</ng-option>
                            <ng-option (click)="selectedTrackerOption = null" value="bulk">
                                <span class="ic-double-upload icon ic-xxs ic-dark mr-8"></span>
                                Bulk Operation</ng-option>
                        </ng-select>
                    </div>
                </ng-container>
                <ng-container
                    *ngIf="currentPath === '/invoice' ? false : permissions?.has('Permissions.Leads.BulkUpload') || permissions?.has('Permissions.Leads.Create')">
                    <div class="btn-left-dropdown ml-10"
                        (click)="permissions?.has('Permissions.Leads.Create') ? navigateToAddLead() : ''">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">{{ 'BUTTONS.add-lead' | translate }}</span>
                    </div>
                    <div class="btn-right-dropdown btn-w-30 black-100">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" ResizableDropdown
                            [(ngModel)]="selectedOption" [ngModelOptions]="{standalone: true}"
                            (click)="openBulkUploadQR()">
                            <ng-option (click)="selectedOption = null" value="bulkUpload"
                                *ngIf="permissions?.has('Permissions.Leads.BulkUpload')">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
                            <ng-option (click)="selectedOption = null" value="QRCode">
                                <span class="ic-qr-code icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.qr-generator' | translate }}</ng-option>
                        </ng-select>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    <ng-container *ngIf="currentPath !== '/invoice'">
        <ng-container *ngIf="!isCustomStatusEnabled; else customStatus">
            <div class="flex-between bg-white border-gray-y">
                <div class="align-center no-validation">
                    <div class="align-center ml-30 lead-dropdown">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" name="leads"
                            [ngClass]="{'hide-dropdown-content': currentPath === '/invoice'}" [items]="visibility"
                            ResizableDropdown (change)="filterFunction(); trackerFirstFilter($event)"
                            bindLabel="displayName" bindValue="value" formControlName="FirstLevelFilter">
                            <ng-template ng-label-tmp let-item="item">
                                <div class="align-center-col header-5 fw-semi-bold">
                                    <div class="mr-10 position-relative text-truncate-1">{{ item?.displayName }}</div>
                                    <div class="text-sm fw-700 position-absolute mt-12 right-30">
                                        <span
                                            *ngIf="!isStatusCountsLoading && !activeLeadsCountIsLoading;else statusLoader">
                                            ({{
                                            item?.displayName === 'All Leads' ? activeLeadsCount['allLeadsCount'] :
                                            item?.displayName === 'Active Leads' ? activeLeadsCount['activeLeadsCount']
                                            :
                                            statusCounts[item?.displayName] || 0
                                            }})
                                        </span>
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item">
                                <div class="align-center header-5 fw-semi-bold mt-10">
                                    <span class="mr-10">{{ item?.displayName }}</span>
                                    <span *ngIf="isStatusCountsLoading || activeLeadsCountIsLoading">
                                        (<div class="container px-4 d-inline">
                                            <ng-container *ngFor="let dot of [1,2,3]">
                                                <div class="dot-falling"></div>
                                            </ng-container>
                                        </div>)
                                    </span>
                                    <span *ngIf="!isStatusCountsLoading && !activeLeadsCountIsLoading">
                                        ({{
                                        item?.displayName === 'All Leads' ? activeLeadsCount['allLeadsCount'] :
                                        item?.displayName === 'Active Leads' ? activeLeadsCount['activeLeadsCount'] :
                                        statusCounts[item?.displayName] || 0
                                        }})
                                    </span>

                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                    <ng-container *ngIf="getFormValue('FirstLevelFilter') === 1 && currentPath !== '/invoice'">
                        <div class="top-navbar-black">
                            <drag-scroll class="d-flex tb-w-100-190 ph-w-100-150 scroll-hide scrollbar"
                                [ngClass]="showLeftNav ? 'w-100-360' : 'w-100-260'">
                                <ul class="py-12">
                                    <li *ngFor="let state of _viewStates">
                                        <a name="state" class="position-relative header-5 ml-30 fw-semi-bold"
                                            [class.active]="getFormValue('SecondLevelFilter') === SecondLevelFilterEnum[state]"
                                            id="clkLeadsCurrent{{ state }}"
                                            data-automate-id="clkLeadsCurrent{{ state }}"
                                            (click)="onSecondLevelFilterClick(SecondLevelFilterEnum[state]); trackerSecFilter(state)">

                                            <span>{{ state }}</span>
                                            <span
                                                *ngIf="!activeLeadsCountIsLoading && !isStatusCountsLoading;else statusLoader">
                                                ({{activeLeadsCount ? state == 'Expression Of Interest' ?
                                                activeLeadsCount['expressionOfInterestLeadCount'] :
                                                activeLeadsCount[((state
                                                |
                                                lowercase))+'LeadsCount'] : 0}})
                                            </span>
                                            <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                                                *ngIf="getFormValue('SecondLevelFilter') === SecondLevelFilterEnum[state]"
                                                (click)="onResetFilter($event,'SecondLevelFilter')"></span>
                                        </a>
                                    </li>
                                </ul>
                            </drag-scroll>
                        </div>
                    </ng-container>
                    <ng-container
                        *ngIf="filtersForm.get('FirstLevelFilter')?.value !== 1 && filtersForm.get('FirstLevelFilter')?.value !== 0 && secondLevelFilterList.length && currentPath !== '/invoice'">
                        <div class="top-navbar-black">
                            <drag-scroll class="d-flex tb-w-100-190 ph-w-100-150 scroll-hide scrollbar"
                                [ngClass]="showLeftNav ? 'w-100-360' : 'w-100-260'">
                                <ul class="py-12">
                                    <li *ngFor="let state of secondLevelFilterList">
                                        <a name="state" class="position-relative header-5 ml-30 fw-semi-bold"
                                            id="clkLeadsCurrent{{ state }}"
                                            data-automate-id="clkLeadsCurrent{{ state }}"
                                            [class.active]="getFormValue('SecondLevelFilterId') === state.id"
                                            (click)="getFormValue('SecondLevelFilterId') !== state.id && filtersForm.get('SecondLevelFilterId').setValue(state.id);filterFunction()">
                                            <span>
                                                {{ state?.displayName }}
                                                <span
                                                    *ngIf="!activeLeadsCountIsLoading && !isStatusCountsLoading; else statusLoader">
                                                    ({{statusCounts[state?.displayName] || 0}})
                                                </span>
                                            </span>
                                            <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                                                *ngIf="getFormValue('SecondLevelFilterId') === state.id"
                                                (click)="onResetFilter($event,'SecondLevelFilterId')"></span>
                                        </a>
                                    </li>
                                </ul>
                            </drag-scroll>
                        </div>
                    </ng-container>
                    <ng-container
                        *ngIf="getFormValue('FirstLevelFilter') === 0 && !activeLeadsCountIsLoading && !isStatusCountsLoading">
                        <div
                            class="top-navbar-black mx-12 text-nowrap w-100-410 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            {{'LEADS.all-leads-msg-1' | translate}} {{activeLeadsCount.allLeadsCount}}
                            {{'LEADS.all-leads-msg-2' |
                            translate}}
                        </div>
                    </ng-container>
                    <ng-container
                        *ngIf="getFormValue('FirstLevelFilter') === 4 && !secondLevelFilterList.length && !statusCounts['Booked'] && !activeLeadsCountIsLoading && !isStatusCountsLoading">
                        <div
                            class="top-navbar-black mx-12 text-nowrap w-100-340 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            {{'LEADS.booked-without-count-msg' | translate}}
                        </div>

                    </ng-container>
                    <ng-container
                        *ngIf="getFormValue('FirstLevelFilter') === 6 && !secondLevelFilterList.length  && !activeLeadsCountIsLoading && !isStatusCountsLoading">
                        <div *ngIf="statusCounts['Invoiced']"
                            class="top-navbar-black mx-12 text-nowrap w-100-340 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            {{'LEADS.booked-with-count-msg' | translate}} {{statusCounts['Invoiced']}} lead(s)
                            invoiced.
                        </div>

                        <div *ngIf="!statusCounts['Invoiced']"
                            class="top-navbar-black mx-12 text-nowrap w-100-340 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            Oops! Looks like no leads have been Invoiced yet.
                        </div>

                    </ng-container>
                    <ng-container
                        *ngIf="getFormValue('FirstLevelFilter') === 5 && !secondLevelFilterList.length  && !activeLeadsCountIsLoading && !isStatusCountsLoading">
                        <div *ngIf="statusCounts['Booking Cancel']"
                            class="top-navbar-black mx-12 text-nowrap w-100-340 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            Oops! {{statusCounts['Booking Cancel']}} leads have cancelled their booking.
                        </div>

                        <div *ngIf="!statusCounts['Booking Cancel']"
                            class="top-navbar-black mx-12 text-nowrap w-100-340 tb-w-100-190 ph-w-100-150 py-12 scrollbar scroll-hide">
                            Hooray!! No leads have cancelled the booking.
                        </div>

                    </ng-container>

                </div>
                <div [ngClass]="{'blinking pe-none': isLeadDataLoading}"
                    class="btn-coal my-4 h-32 min-w-32 mr-30 ph-d-none ml-10" title="Refresh Data"
                    (click)="filterFunction()"><span class="icon ic-refresh ic-xxs"></span></div>
            </div>
            <ng-container
                *ngIf="(SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Scheduled' || SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Overdue') && getFormValue('FirstLevelFilter')">
                <div class="align-center lead-dropdown bg-white no-validation">
                    <ng-select *ngIf="SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Scheduled'"
                        [virtualScroll]="true" ResizableDropdown [searchable]="false" [clearable]="false" name="leads"
                        class="ml-50 no-validation" formControlName="ScheduledDateTypeFilter" (change)="filtersForm.patchValue({        pageNumber:1
                        });filterFunction()">
                        <ng-option *ngFor="let date of dateFilters; let i = index" [value]="i" class="align-center">
                            {{ date }}
                            <span *ngIf="!activeLeadsCountIsLoading && !isStatusCountsLoading">
                                ({{activeLeadsCount[date] || 0}})
                            </span>
                            <span *ngIf="activeLeadsCountIsLoading || isStatusCountsLoading">
                                (<div class="container px-4 d-inline">
                                    <ng-container *ngFor="let dot of [1,2,3]">
                                        <div class="dot-falling"></div>
                                    </ng-container>
                                </div>)
                            </span>
                        </ng-option>
                    </ng-select>
                    <div class="top-navbar-black"
                        [ngClass]="{'ml-20' : SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Overdue'}">
                        <ul class="py-12 scrollbar scroll-hide"
                            [ngClass]="SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Overdue'  ? 'ph-w-100-40' : (SecondLevelFilterEnum[getFormValue('SecondLevelFilter')] === 'Scheduled' && getFormValue('ScheduledDateTypeFilter') === 0) ? 'tb-w-100-135' : 'tb-max-w-100-230'">
                            <li *ngFor="let scheduled of scheduledTodayList">
                                <a name="scheduled" class="position-relative header-5 ml-30 fw-semi-bold"
                                    id="clkLeadsCurrent{{scheduled}}"
                                    [class.active]="getFormValue('ScheduledType') === LeadScheduledTypeEnum[scheduled]"
                                    (click)="getFormValue('ScheduledType') !== LeadScheduledTypeEnum[scheduled] && filtersForm.get('ScheduledType').setValue(LeadScheduledTypeEnum[scheduled]);filtersForm.patchValue({        pageNumber:1
                                    });filterFunction()" data-automate-id="clkLeadsCurrent{{scheduled}}"><span>{{
                                        scheduled === 'Site visits' && globalSettingsData?.shouldRenameSiteVisitColumn ?
                                        'Referral' : scheduled }}
                                    </span>

                                    <span
                                        *ngIf="!activeLeadsCountIsLoading && !isStatusCountsLoading; else statusLoader">
                                        ({{activeLeadsCount[scheduled] || 0}})
                                    </span>
                                    <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                                        *ngIf="getFormValue('ScheduledType') === LeadScheduledTypeEnum[scheduled]"
                                        (click)="onResetFilter($event,'ScheduledType')"></span>
                                </a>
                            </li>
                        </ul>
                    </div>

                </div>
            </ng-container>
        </ng-container>
        <ng-template #customStatus>
            <div class="flex-between bg-white border-gray-y" *ngIf="customTopLevelFilters?.length">
                <div class="align-center no-validation">
                    <div class="align-center ml-30 lead-dropdown">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                            [ngClass]="{'hide-dropdown-content': currentPath === '/invoice'}"
                            [items]="customTopLevelFilters" class="w-105" bindLabel="name" bindValue="id"
                            formControlName="CustomFirstLevelFilter" ResizableDropdown
                            (change)="onCustomFirstLevelFilterChange();filterFunction()">
                            <ng-template ng-label-tmp let-item="item">
                                <div class="align-center-col header-5 fw-semi-bold">
                                    <div class="mr-10 position-relative text-truncate-1">{{ item?.name }}</div>
                                    <div class="text-sm fw-700 position-absolute mt-12 right-30">
                                        <span *ngIf="!isCustomTopFiltersLoading; else statusLoader">({{
                                            customFiltersCount[item?.id] }})</span>
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item">
                                <div class="align-center header-5 fw-semi-bold mt-10">
                                    <span class="mr-10">{{ item?.name }}</span>
                                    <span>
                                        <ng-container *ngIf="!isCustomTopFiltersLoading;else statusLoader">
                                            ({{ customFiltersCount[item?.id]}})
                                        </ng-container>

                                    </span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                    <ng-container
                        *ngIf="getFormValue('CustomFirstLevelFilter') && customSecondLevelFilterList?.length && currentPath !== '/invoice'">
                        <div class="top-navbar-black">
                            <drag-scroll class="d-flex tb-w-100-190 ph-w-100-150 scroll-hide scrollbar"
                                [ngClass]="showLeftNav ? 'w-100-360' : 'w-100-260'">
                                <ul class="py-12">
                                    <li *ngFor="let filter of customSecondLevelFilterList">
                                        <a name="filter" class="position-relative header-5 ml-30 fw-semi-bold"
                                            id="clkLeadsCurrent{{ filter }}"
                                            data-automate-id="clkLeadsCurrent{{ filter }}"
                                            [class.active]="getFormValue('CustomSecondLevelFilter') === filter.id"
                                            (click)="onCustomSecondLevelFilterClick(filter.id);filterFunction()">
                                            <span>
                                                {{ filter?.name }}
                                                <span *ngIf="!isCustomTopFiltersLoading; else statusLoader">
                                                    ({{ customFiltersCount[filter?.id] }})
                                                </span>
                                            </span>
                                            <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                                                *ngIf="getFormValue('CustomSecondLevelFilter') === filter?.id"
                                                (click)="onResetFilter($event,'CustomSecondLevelFilter')"></span>
                                        </a>
                                    </li>
                                </ul>
                            </drag-scroll>
                        </div>
                    </ng-container>

                </div>
                <div [ngClass]="{'blinking pe-none': isLeadDataLoading}"
                    class="btn-coal my-4 h-32 min-w-32 mr-30 ph-d-none ml-10" title="Refresh Data"
                    (click)="filterFunction()"><span class="icon ic-refresh ic-xxs"></span></div>
            </div>
            <ng-container *ngIf="getFormValue('CustomSecondLevelFilter') && customThirdLevelFilterList?.length">
                <div class="align-center lead-dropdown bg-white no-validation">
                    <!-- <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" name="leads"
                        class="ml-50 no-validation" formControlName="CustomThirdLevelFilter">
                        <ng-option *ngFor="let filter of customThirdLevelFilterList" [value]="filter?.id"
                            class="align-center">
                            {{ filter?.name }}
                            <span *ngIf="!isCustomTopFiltersLoading">
                                ({{customFiltersCount[filter?.id] || 0}})
                            </span>
                            <ng-container *ngIf="isCustomTopFiltersLoading"
                                [ngTemplateOutlet]="statusLoader"></ng-container>
                        </ng-option>
                    </ng-select> -->
                    <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" name="leads"
                        formControlName="CustomThirdLevelFilter" (change)="filterFunction()" ResizableDropdown
                        class="ml-50 no-validation">
                        <ng-option *ngFor="let filter of customThirdLevelFilterList" [value]="filter?.id"
                            class="align-center">{{filter?.name}}
                            <span class="header-5 fw-semi-bold mr-10" *ngIf="!isCustomTopFiltersLoading">
                                ({{customFiltersCount[filter?.id] || 0}})
                            </span>
                            <span *ngIf="isCustomTopFiltersLoading">
                                (<div class="container px-4 d-inline">
                                    <ng-container *ngFor="let dot of [1,2,3]">
                                        <div class="dot-falling"></div>
                                    </ng-container>
                                </div>)
                            </span>
                        </ng-option>
                    </ng-select>
                    <div class="top-navbar-black ml-20" *ngIf="customFourthLevelFilterList?.length">
                        <ul class="py-12 scrollbar scroll-hide">
                            <li *ngFor="let filter of customFourthLevelFilterList">
                                <a name="scheduled" class="position-relative header-5 ml-30 fw-semi-bold"
                                    id="clkLeadsCurrent{{filter}}"
                                    [class.active]="getFormValue('CustomFourthLevelFilter') === filter?.id"
                                    (click)="getFormValue('CustomFourthLevelFilter') !== filter.id && filtersForm.get('CustomFourthLevelFilter').setValue(filter?.id); filterFunction()"
                                    data-automate-id="clkLeadsCurrent{{filter}}"><span>{{ filter.name === 'Site visits'
                                        && globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral' : filter.name }}

                                        <!-- <ng-container *ngIf="!activeLeadsCountIsLoading">
                                        ({{ appliedFilter.secondLevelFilter === 'Scheduled' ? (activeLeadsCount ?
                                        activeLeadsCount[( scheduled
                                        === 'Site visits' ? 'siteVisits' :
                                        scheduled === 'Callbacks' ? 'callback' : (scheduled | lowercase))+'Count'] : 0) :
                                        (activeLeadsCount ?
                                        activeLeadsCount[( scheduled === 'Site visits' ? 'overdueSiteVisit' :
                                        scheduled === 'Callbacks' ? 'overdueCallback' : 'overdueMeeting')+'Count'] : 0) }})
                                    </ng-container>
                                    <span *ngIf="activeLeadsCountIsLoading">
                                        (<div class="container px-4 d-inline">
                                            <ng-container *ngFor="let dot of [1,2,3]">
                                                <div class="dot-falling"></div>
                                            </ng-container>
                                        </div>)
                                    </span> -->
                                        <span *ngIf="!isCustomTopFiltersLoading;else statusLoader">
                                            ({{customFiltersCount[filter?.id] || 0}})
                                        </span>
                                    </span>
                                    <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                                        *ngIf="getFormValue('CustomFourthLevelFilter') === filter?.id"
                                        (click)="onResetFilter($event,'CustomFourthLevelFilter')"></span>
                                </a>
                            </li>
                        </ul>
                    </div>

                </div>
            </ng-container>
        </ng-template>
    </ng-container>
    <ng-container *ngIf="currentPath === '/invoice'">
        <div class="flex-between bg-white border-gray-y ph-p-10">
            <div class="align-center no-validation">
                <div class="align-center ml-30 lead-dropdown">
                    <a class="header-5 fw-semi-bold" [class.active]="true">Invoiced
                        <span *ngIf="!isLeadDataLoading; else statusLoader">({{leadsTotalCount}})</span></a>
                </div>

            </div>
            <div [ngClass]="{'blinking pe-none': isLeadDataLoading}"
                class="btn-coal my-4 h-32 min-w-32 mr-30 ph-d-none ml-10" title="Refresh Data"
                (click)="filterFunction()"><span class="icon ic-refresh ic-xxs"></span></div>
        </div>
    </ng-container>
    <ng-container
        *ngIf="isCustomStatusEnabled && !customTopLevelFilters?.length && isCustomTopFiltersLoading && currentPath !== '/invoice'"
        [ngTemplateOutlet]="leadsLoader">

    </ng-container>
    <ng-container
        *ngIf="isCustomStatusEnabled ? customTopLevelFilters?.length || !isCustomTopFiltersLoading || currentPath === '/invoice' : true">
        <div class="pt-16 px-30" [ngClass]="showWebUI ? 'hidden-web' : 'hidden-phone'">
            <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
                <div class="align-center flex-grow-1 no-validation border-end tb-br-0">
                    <div *ngIf="currentPath === '/invoice' ? permissions?.has('Permissions.Invoice.Search') : permissions?.has('Permissions.Leads.Search')"
                        class="position-relative flex-grow-1" id="search-dropdown">
                        <div class="align-center w-100 px-10 py-12">
                            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
                            <input type="text" (input)="isEmptyInput()" placeholder="type to search"
                                [(ngModel)]="searchTerm" (keydown.enter)="onSearch()"
                                [ngModelOptions]="{standalone: true}" class="border-0 outline-0 w-100" />
                            <span class="icon ic-triangle ic-xxxs ic-dark cursor-pointer"
                                (click)="toggleSearchDropdown(); $event.stopPropagation()"></span>
                        </div>
                        <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
                            ({{ 'LEADS.lead-search-prompt' | translate }})</small>

                        <!-- Search Enhancement Dropdown -->
                        <div [ngClass]="{'pe-none': isModuleWiseSearchPropertiesLoading}" *ngIf="showSearchDropdown"
                            class="position-absolute w-100 bg-white brbr-20 brbl-20 left-0 top-40 border shadow-sm z-index-1001"
                            (clickOutside)="showSearchDropdown = false">
                            <div class="scrollbar scroll-hide max-h-100-360">
                                <div class="d-flex flex-wrap p-10">
                                    <h6 *ngFor="let filter of getDisplayedFilters()"
                                        class="px-10 fw-semi-bold br-6 py-6 m-4 cursor-pointer border"
                                        [ngClass]="{'text-muted': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter)}"
                                        [ngStyle]="{'pointer-events': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? 'none' : 'auto',
                                                   'opacity': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? '0.5' : '1'}"
                                        (click)="toggleSearchFilter(filter);$event.stopPropagation()">
                                        {{ filter?.displayName }}
                                    </h6>
                                </div>
                            </div>
                            <div *ngIf="getSelectedFilters().length" class="p-10">
                                <h5 class="fw-300 mb-6">{{recentSearches?.length ? 'Recent Searches' : 'Selected'}}</h5>
                                <div class="d-flex flex-wrap">
                                    <div *ngFor="let filter of getSelectedFilters()"
                                        class="px-10 py-6 m-4 d-flex bg-white-100 align-center cursor-pointer rounded-pill bg-slate">
                                        <div (click)="toggleSearchFilter(filter)">
                                            {{ filter?.displayName }}
                                        </div>
                                        <div class="ml-8 bg-black-900 align-center p-4 br-20"
                                            (click)="toggleSearchFilter(filter); $event.stopPropagation();">
                                            <span class="icon ic-cancel ic-xx-xs"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-10 d-flex align-center">
                                <span *ngIf="selectedSearchFilters.length >= 5" class="text-danger">Users can
                                    select
                                    up
                                    to five (5) parameters at a time</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex-end">
                        <div *ngIf="(currentPath === '/invoice' ? permissions?.has('Permissions.Invoice.Export') : permissions?.has('Permissions.Leads.Export') && globalSettingsData?.isLeadsExportEnabled) && rowData?.length"
                            (click)="exportLeadReport()"
                            class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top">
                            {{ 'REPORTS.export' | translate }}</div>
                    </div>
                </div>
                <div class="tb-br-top align-center ip-flex-col ip-align-center-unset">
                    <div class="d-flex w-100">
                        <!-- <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
                            (click)="openAdvFiltersModal()">
                            <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
                            <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
                        </div> -->
                        <div class="flex-center">
                            <div class="filters-grid clear-padding border-end h-100 ip-br-0">
                                <div class="align-center h-100 ml-16 tb-ml-0">
                                    <div class="bg-white manage-select datefilter-scroll">
                                        <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}"
                                            [searchable]="false" class="lead-date ip-max-w-80px min-w-60"
                                            ResizableDropdown [(ngModel)]="dateType"
                                            [ngModelOptions]="{standalone: true}" (change)="dateChange()">
                                            <ng-option name="dateType" ngDefaultControl
                                                *ngFor="let dType of dateTypeList" [value]="dType">{{dType}}</ng-option>
                                        </ng-select>
                                    </div>
                                    <div class="date-picker border-start-0 align-center" id="leadsAppointmentDate"
                                        data-automate-id="leadsAppointmentDate">
                                        <span class="ic-appointment icon ic-xxs ic-black"
                                            [owlDateTimeTrigger]="dt1"></span>
                                        <input type="text" readonly placeholder="ex. 19-06-2025 - 29-06-2025"
                                            class="pl-20 text-large" [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1"
                                            [selectMode]="'range'" (ngModelChange)="filterDate = $event; dateChange()"
                                            [ngModelOptions]="{standalone: true}" [ngModel]="filterDate" />
                                        <owl-date-time [pickerType]="'calendar'" #dt1
                                            (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="getFormValue('fromDate')"
                                class="bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 align-center cursor-pointer"
                                (click)="filtersForm.patchValue({dateType:0,fromDate:null,toDate:null});filterFunction()">
                                <span class="ip-d-none">{{ 'GLOBAL.reset' | translate }}</span> <span
                                    class="ic-refresh d-none ip-d-block"></span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex ip-br-top">
                        <div class="align-center position-relative cursor-pointer d-flex border-end">
                            <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
                                {{ 'BUTTONS.manage-columns' | translate }}</span>
                            <div class="show-hide-gray w-140">
                                <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true"
                                    [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                                    [ngModelOptions]="{standalone: true}" ResizableDropdown
                                    (change)="onColumnsSelected($event); trackingService.trackFeature('Web.Leads.Options.ManageColumns.Click')">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container" [title]="item.label"><input type="checkbox"
                                                id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected"><span class="checkmark"></span>{{item.label}}
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <div class="px-12 ip-w-30px  align-center cursor-pointer"
                            (click)="onSetColumnDefault();trackingService.trackFeature('Web.Data.Button.Default.Click')">
                            <span class="ic-swap icon ic-dark ic-xs"></span>
                        </div>
                        <div class="bg-coal text-white p-12 align-center cursor-pointer"
                            (click)="openAdvFiltersModal()">
                            <span class="icon ic-sliders ic-white ic-xxs mr-8"></span>
                            <span>Filter</span>
                        </div>
                        <div class="flex-end bg-black-10 px-12 position-relative" id="saved-filter"
                            (click)="openSavedFilter($event)">
                            <span class="icon ic-triangle ic-white ic-x-xs mx-2"></span>
                            <saved-filter (closeFilter)="closeSavedFilter()" [isMobileView]="isMobileView"
                                [showFilters]="showFilters" (selectFilter)="handleSelectFilter($event)"
                                (editFilter)="handleEditFilter($event)" [filters]="filters"
                                *ngIf="isSavedFilterOpen"></saved-filter>
                        </div>
                        <div class="show-dropdown-white align-center position-relative">
                            <ng-select [virtualScroll]="true" [placeholder]="pageSize" class="w-75px"
                                [searchable]="false" ResizableDropdown formControlName="pageSize"
                                (change)="onPageSizeChange($event)">
                                <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize"
                                    [value]="pageSize">
                                    {{pageSize}}</ng-option>
                            </ng-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white px-4 py-12 tb-w-100-40" [ngClass]="showLeftNav ? 'w-100-200' : 'w-100-100'">
                <ng-container *ngIf="showFilters">
                    <div class="bg-secondary flex-between">
                        <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                            <ng-container *ngFor="let filter of filteredFilters | keyvalue">
                                <div class="d-flex" *ngIf="!isArray(filter.value) && filter.value">
                                    <div
                                        class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                        {{leadsFiltersKeyLabel[filter.key] ??
                                        filter.key}}: {{getFilterValue(filter.key,filter.value)}}<span
                                            class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                            (click)="onRemoveFilter(filter.key,value,filter.value)"></span></div>
                                </div>
                                <ng-container *ngIf="isArray(filter.value)&& filter.value?.length">
                                    <ng-container *ngFor="let value of filter.value">
                                        <div class="d-flex">
                                            <div
                                                class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                                {{leadsFiltersKeyLabel[filter.key] ??
                                                filter.key}}: {{getFilterValue(filter.key,value)}}<span
                                                    class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                                    (click)="onRemoveFilter(filter.key,value,filter.value)"></span>
                                            </div>
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </drag-scroll>
                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                            (click)="onSaveFilter(addFile)" *ngIf="!selectedFilter">Save Filter</div>
                        <div (click)="openAdvFiltersModal()"
                            class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                            *ngIf="isFilterSelected">Edit Filter</div>
                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                            *ngIf="isFilterUpdated" (click)="onSaveFilter(addFile)">Update Filter</div>
                        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                            (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}
                        </div>
                    </div>
                </ng-container>
            </div>
            <ng-container *ngIf="!isLeadDataLoading; else leadsLoader">
                <div class="manage-leads pinned-grid checkbox-align-h-60" *ngIf="rowData?.length; else noData">
                    <ag-grid-angular #agGrid class="ag-theme-alpine" (sortChanged)="onSortChanged($event)"
                        [gridOptions]="gridOptions" [rowData]="rowData" [icons]="gridOptions?.icons"
                        [suppressPaginationPanel]="true" [suppressMovableColumns]="false"
                        [suppressDragLeaveHidesColumns]="true" [suppressMultiSort]="true"
                        [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true"
                        (gridReady)="onGridReady($event)" (cellClicked)="onCellClicked($event)"
                        (filterChanged)="onFilterChanged($event)">
                    </ag-grid-angular>
                    <div class="flex-end my-20 ph-flex-col ph-flex-start">
                        <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
                            {{(filtersPayload?.pageNumber-1)*filtersPayload?.pageSize + 1}}
                            {{ 'GLOBAL.to-small' | translate }}
                            {{(filtersPayload?.pageNumber-1)*filtersPayload?.pageSize +
                            rowData?.length}}
                            {{ 'GLOBAL.of-small' | translate }} {{leadsTotalCount}} {{ 'GLOBAL.entries-small' |
                            translate }}
                        </div>
                        <pagination [offset]="filtersPayload?.pageNumber-1" [limit]="1" [range]="1"
                            [size]='getPages(leadsTotalCount,filtersPayload?.pageSize)'
                            (pageChange)="this.filtersForm.patchValue({pageNumber: $event+1});filterFunction(); trackingService.trackFeature('Web.Leads.Button.Pagination.Click')">
                        </pagination>
                    </div>
                    <!-- <div class="my-20">
                        <div class="flex-between tb-flex-col tb-flex-start">
                            <div class="d-flex w-100-735 tb-w-100-60 overflow-auto scrollbar-sm p-4">
                                <ng-container *ngFor="let leadTag of leadTags">
                                    <div class="align-center gap-1 mr-10 cursor-pointer"
                                        (click)="filterTag(leadTag.name)"
                                        [ngClass]="{'fw-600' : filtersForm?.value?.customFlags?.includes(leadTag.name)}">
                                        <img [appImage]="filtersForm?.value?.customFlags?.includes(leadTag.name) ? leadTag.activeImagePath : leadTag.inactiveImagePath"
                                            [type]="'defaultTagsImg'" class="w-16 h-16" alt="Flag Image">
                                        <p class="text-nowrap">{{leadTag.name}} ({{ getTagsCounts(leadTag.name) }})</p>
                                    </div>
                                </ng-container>
                            </div>
                            <div class="flex-end ip-mt-10 tb-pt-10">
                                <div class="mr-10">{{ 'GLOBAL.showing' | translate }}
                                    {{(filtersPayload?.pageNumber-1)*filtersPayload?.pageSize + 1}}
                                    {{ 'GLOBAL.to-small' | translate }}
                                    {{(filtersPayload?.pageNumber-1)*filtersPayload?.pageSize +
                                    rowData?.length}}
                                    {{ 'GLOBAL.of-small' | translate }} {{leadsTotalCount}} {{ 'GLOBAL.entries-small' |
                                    translate }}
                                </div>
                                <pagination [offset]="filtersPayload?.pageNumber-1" [limit]="1" [range]="1"
                                    [size]='getPages(leadsTotalCount,filtersPayload?.pageSize)'
                                    (pageChange)="this.filtersForm.patchValue({pageNumber: $event+1});filterFunction(); trackingService.trackFeature('Web.Leads.Button.Pagination.Click')">
                                </pagination>
                            </div>
                        </div>
                    </div> -->
                </div>

            </ng-container>
        </div>
        <div class="w-100 bg-brick-pattern" id="data-container" [ngClass]="showWebUI ? 'hidden-phone' : 'hidden-web'">
            <div class="pt-10 px-24 pr-10">
                <div class="align-center">
                    <div class="align-center flex-grow-1 no-validation p-8 bg-white border-gray br-8 position-relative"
                        id="search-dropdown">
                        <input type="text" (input)="isEmptyInput()" placeholder="type to search"
                            [(ngModel)]="searchTerm" (keydown.enter)="onSearch()" [ngModelOptions]="{standalone: true}"
                            class="border-0 outline-0 w-100" />
                        <span class="icon ic-triangle ic-xxxs ic-dark cursor-pointer"
                            (click)="toggleSearchDropdown(); $event.stopPropagation()"></span>

                        <!-- Mobile Search Enhancement Dropdown -->
                        <div [ngClass]="{'pe-none': isModuleWiseSearchPropertiesLoading}" *ngIf="showSearchDropdown"
                            class="position-absolute w-100 bg-white brbr-20 brbl-20 left-0 top-40 border shadow-sm z-index-1001"
                            (clickOutside)="showSearchDropdown = false">
                            <div class="scrollbar scroll-hide max-h-100-360">
                                <div class="d-flex flex-wrap p-10">
                                    <h6 *ngFor="let filter of getDisplayedFilters()"
                                        class="px-10 fw-semi-bold br-6 py-6 m-4 cursor-pointer border"
                                        [ngClass]="{'text-muted': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter)}"
                                        [ngStyle]="{'pointer-events': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? 'none' : 'auto',
                                                   'opacity': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? '0.5' : '1'}"
                                        (click)="toggleSearchFilter(filter);$event.stopPropagation()">
                                        {{ filter?.displayName }}
                                    </h6>
                                </div>
                            </div>
                            <div *ngIf="getSelectedFilters().length" class="p-10">
                                <h5 class="fw-300 mb-6">{{recentSearches?.length ? 'Recent Searches' : 'Selected'}}</h5>
                                <div class="d-flex flex-wrap">
                                    <div *ngFor="let filter of getSelectedFilters()"
                                        class="px-10 py-6 m-4 d-flex bg-white-100 align-center cursor-pointer rounded-pill bg-slate">
                                        <div (click)="toggleSearchFilter(filter)">
                                            {{ filter?.displayName }}
                                        </div>
                                        <div class="ml-8 bg-black-900 align-center p-4 br-20"
                                            (click)="toggleSearchFilter(filter); $event.stopPropagation();">
                                            <span class="icon ic-cancel ic-xx-xs"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-10 d-flex align-center">
                                <span *ngIf="selectedSearchFilters.length >= 5" class="text-danger">Users can
                                    select
                                    up
                                    to five (5) parameters at a time</span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex border-gray bg-white mx-10 cursor-pointer br-8">
                        <div class="p-8" (click)="openAdvFiltersModal()">
                            <span class="icon ic-right-left ic-sm ic-slate-90"></span>
                        </div>
                        <div class="flex-end bg-white brtr-8 brbr-8 p-8 position-relative" id="saved-filter"
                            (click)="openSavedFilter($event); $event.stopPropagation()">
                            <span class="icon ic-triangle ic-dark ic-x-xs mx-2"></span>
                            <saved-filter [isMobileView]="isMobileView" [showFilters]="showFilters"
                                (closeFilter)="closeSavedFilter()" (selectFilter)="handleSelectFilter($event)"
                                (editFilter)="handleEditFilter($event)" [filters]="filters"
                                *ngIf="isSavedFilterOpen"></saved-filter>
                        </div>
                    </div>
                </div>
            </div>

            <ng-container *ngIf="(cardData?.length && isMobileView && showWebUI ) else phLoader">
                <ng-container *ngIf="showFilters && !isLeadDataLoading">
                    <div class="bg-secondary flex-between  mx-24 my-8 mr-20">
                        <div class="d-flex bg-white br-6">
                            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100-190">
                                <ng-container *ngFor="let filter of filteredFilters | keyvalue">
                                    <div class="d-flex" *ngIf="!isArray(filter.value) && filter.value">
                                        <div
                                            class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                            {{leadsFiltersKeyLabel[filter.key] ??
                                            filter.key}}: {{getFilterValue(filter.key,filter.value)}}<span
                                                class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                                (click)="onRemoveFilter(filter.key,value,filter.value)"></span></div>
                                    </div>
                                    <ng-container *ngIf="isArray(filter.value) && filter.value?.length">
                                        <ng-container *ngFor="let value of filter.value">
                                            <div class="d-flex">
                                                <div
                                                    class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap">
                                                    {{leadsFiltersKeyLabel[filter.key] ??
                                                    filter.key}}: {{getFilterValue(filter.key,value)}}<span
                                                        class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                                                        (click)="onRemoveFilter(filter.key,value,filter.value)"></span>
                                                </div>
                                            </div>
                                        </ng-container>

                                    </ng-container>

                                </ng-container>
                            </drag-scroll>
                            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                (click)="onSaveFilter(addFile)" *ngIf="!selectedFilter">Save Filter</div>
                            <div (click)="openAdvFiltersModal()"
                                class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                *ngIf="isFilterSelected">Edit Filter</div>
                            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                *ngIf="isFilterUpdated" (click)="onSaveFilter(addFile)">Update Filter</div>
                            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                                (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' |
                                translate}}
                            </div>
                        </div>
                    </div>
                </ng-container>
                <div class="my-10 ml-20 mr-30 p-6 bg-white text-coal br-6 fw-600 text-sm cursor-pointer"
                    *ngFor="let data of cardData">
                    <div class="align-center mb-6 flex-between w-100-60 scrollbar-sm overflow-auto">
                        <div class="align-center text-white text-xxs">
                            <div [title]="data?.meetingsDone?.length + ' Meeting Done'"
                                *ngIf="data?.meetingsDone?.length"
                                class="mr-4 py-4 px-8 bg-green-70 text-nowrap cursor-pointer brbr-4 brbl-4"
                                (click)="openLocationModal(data, true)">
                                {{ 'LEAD_FORM.meetings-done'| translate }} - {{ data?.meetingsDone?.length }}</div>
                            <div [title]="data?.meetingsNotDone?.length +' Meeting Not Done'"
                                *ngIf="data?.meetingsNotDone?.length"
                                class="mr-4 py-4 px-8 bg-red-70 text-nowrap cursor-pointer brbr-4 brbl-4">
                                {{ 'LEAD_FORM.meetings-not-done' | translate }} - {{ data?.meetingsNotDone?.length }}
                            </div>
                            <div [title]="data?.siteVisitsDone?.length +' Site Visit Done'"
                                *ngIf="data?.siteVisitsDone?.length"
                                class="mr-4 py-4 px-8 bg-green-70 text-nowrap cursor-pointer brbr-4 brbl-4"
                                (click)="openLocationModal(data)">{{
                                'LEAD_FORM.visits-done' | translate }} - {{ data?.siteVisitsDone?.length }}</div>
                            <div [title]="data?.siteVisitsNotDone?.length +' Site Visit Not Done'"
                                *ngIf="data?.siteVisitsNotDone?.length"
                                class="mr-4 py-4 px-8 bg-red-70 text-nowrap cursor-pointer brbr-4 brbl-4">
                                {{ 'LEAD_FORM.visits-not-done' | translate }} - {{ data?.siteVisitsNotDone?.length }}
                            </div>
                        </div>
                        <div class="flex-center">
                            <ng-container *ngFor="let leadTag of data.customFlags">
                                <div [title]="leadTag?.flag.name">
                                    <img [type]="'leadrat'"
                                        [appImage]="leadTag.flag.isActive ? leadTag?.flag?.activeImagePath : leadTag?.flag?.inactiveImagePath"
                                        class="w-10 h-10 ml-4" alt="Flag Image">
                                </div>
                            </ng-container>
                        </div>
                    </div>
                    <div class="bg-white-100 p-10 br-4" (click)="openLeadPreviewModal(data,true)">
                        <div class="align-center position-relative">
                            <div class="dot-lg-xxl dot mr-6 bg-white">
                                <div class="text-coal">{{ data.name ? data.name.split(' ')[0][0] + (data.name.split('
                                    ')[1] ?
                                    data.name.split(' ')[1][0] : '') : '--' }}</div>
                            </div>
                            <div class="justify-between text-xs fw-semi-bold w-100">
                                <div>
                                    <div class="text-large max-w-170 text-truncate">{{ data.name ? data.name : '--' }}
                                    </div>
                                    <div class="d-flex w-170 mt-4">
                                        <div class="align-center w-50" *ngIf="data.assignTo !== EMPTY_GUID">
                                            <div class="bg-slate-250 text-white dot dot-sm text-sm mr-10">P</div>
                                            <div class="text-truncate mr-10">{{getAssignedToDetails(data?.assignTo,
                                                allUsers, true) || ''}}
                                            </div>
                                        </div>
                                        <div class="align-center w-50"
                                            *ngIf="data.secondaryUserId && globalSettingsData?.isDualOwnershipEnabled">
                                            <div class=" bg-slate-250 text-white dot dot-sm text-sm mr-10">S</div>
                                            <div class="text-truncate">{{getAssignedToDetails(data?.secondaryUserId,
                                                allUsers, true) || ''}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="align-center flex-wrap mt-4">
                                        <div class="align-center" *ngIf="data.enquiry?.propertyTypes?.length">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <ng-container *ngIf="data?.enquiry?.propertyTypes?.length; else noSubtype">
                                                <ng-container *ngFor="let type of data?.enquiry?.propertyTypes">
                                                    <span *ngIf="type?.childType?.displayName">
                                                        {{ type.childType.displayName }}
                                                    </span>
                                                </ng-container>
                                            </ng-container>
                                        </div>
                                        <div class="align-center"
                                            *ngIf="data.enquiry?.bhKs && data.enquiry?.bhKs?.length > 0">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <span>{{getBHKNo(data.enquiry?.bhKs)}}</span>
                                        </div>
                                        <div class="align-center"
                                            *ngIf="data?.enquiry?.lowerBudget || data?.enquiry?.upperBudget">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <span>{{ formatBudget(data?.enquiry?.lowerBudget,data?.enquiry?.currency ||
                                                defaultCurrency) }}
                                                <span
                                                    *ngIf="data?.enquiry?.lowerBudget && data?.enquiry?.upperBudget">-</span>
                                                {{ formatBudget(data?.enquiry?.upperBudget,data?.enquiry?.currency ||
                                                defaultCurrency) }}</span>
                                        </div>
                                    </div>
                                    <div class="align-center text-nowrap flex-wrap text-truncate">
                                        <div class="align-center" *ngIf="data.status?.displayName">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <span>{{data.status?.displayName}}</span>
                                        </div>
                                        <div class="align-center" *ngIf="data.status?.childType?.displayName">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <span>{{data.status?.childType?.displayName}}</span>
                                        </div>
                                        <div class="align-center" *ngIf="data?.scheduledDate">
                                            <span class="dot dot-xxs bg-dark-700 mx-4"></span>
                                            <span>{{data.scheduledDate
                                                ? getTimeZoneDate(data.scheduledDate,
                                                userData?.timeZoneInfo?.baseUTcOffset, 'dateWithTime')
                                                : "---"}}</span>
                                        </div>
                                    </div>
                                    <h6 class="animated-text fw-700 mt-4 text-red-450" *ngIf="!data?.isPicked">
                                        (Untouched)</h6>
                                </div>
                                <div class="justify-between align-items-end flex-col text-nowrap">
                                    <div class="mt-4" *ngIf="permissions.has('Permissions.Leads.ViewLeadSource')">
                                        {{LeadSource[data.enquiry?.leadSource]}}
                                    </div>
                                    <ng-container *ngIf="permissions.has('Permissions.Leads.ViewDuplicateTag')">
                                        <div class="text-sm align-center text-light-orange my-4">
                                            <div [title]="'Total Duplicates :' +data?.childLeadsCount"
                                                *ngIf="data?.childLeadsCount">(TD-{{data?.childLeadsCount}})</div>
                                            <div [title]="'Duplicate Lead Version :'  +data?.duplicateLeadVersion"
                                                *ngIf="data?.duplicateLeadVersion">({{data?.duplicateLeadVersion}})
                                            </div>
                                        </div>
                                    </ng-container>
                                    <ng-container>
                                        <div
                                            *ngIf="getAssignedToDetails(data?.assignTo, allUsers)?.imageUrl else characters">
                                            <img [type]="'leadrat'"
                                                [appImage]="s3BucketUrl+getAssignedToDetails(data?.assignTo, allUsers)?.imageUrl"
                                                width="20" height="20" class="br-50">
                                        </div>
                                        <ng-template #characters>
                                            <div class="text-uppercase dot dot-md bg-white"><span
                                                    class="text-black-200">
                                                    {{data.assignTo !== EMPTY_GUID ?
                                                    ((getAssignedToDetails(data?.assignTo,
                                                    allUsers)?.firstName[0]
                                                    ||
                                                    "") +
                                                    (getAssignedToDetails(data?.assignTo, allUsers)?.lastName[0] || ""))
                                                    :
                                                    ''}}</span>
                                            </div>
                                        </ng-template>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </div>
                    <leads-actions [data]="data" [isCardView]="true" class="card-preview"></leads-actions>
                </div>
                <div *ngIf="!isLeadDataLoading else leadsLoader" class="h-20px" inView (inView)="onInView($event)">
                </div>
            </ng-container>
            <ng-template #phLoader>
                <ng-container *ngIf="!isLeadDataLoading && !cardData.length else leadsLoader">
                    <div class="flex-center-col h-100-250">
                        <img src="assets/images/layered-cards.svg" alt="No lead found">
                        <div class="header-3 fw-600 text-center">{{'PROFILE.no-lead-found' |translate}}</div>
                    </div>
                </ng-container>
            </ng-template>
        </div>
        <div class="justify-center">
            <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 tb-flex-col z-index-2"
                [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
                <leads-bulk-update [gridApi]="gridApi" [appliedFilter]="filtersPayload" [selectedNodes]="selectedNodes"
                    (openBulkUpdateStatusModalEvent)="openBulkUpdateStatusModal(BulkUpdateStatusModal)"
                    (openBulkReassignModalEvent)="openBulkReassignModal(BulkReassignModal)">
                </leads-bulk-update>
            </div>
        </div>
    </ng-container>
    <button class="ph-d-block d-none btn btn-accent-green h-32 position-absolute bottom-40 right-15 icon"
        (click)="toggleUI()">
        <span class="position-relative right-5 ph-d-block d-none"
            [ngClass]="{'ic-flower': !showWebUI, 'ic-lines': showWebUI}"></span>
    </button>
</div>
<ng-template #statusLoader>
    (<div class="container px-4 d-inline">
        <ng-container *ngFor="let dot of [1, 2, 3]">
            <div class="dot-falling"></div>
        </ng-container>
    </div>)
</ng-template>
<ng-template #leadsLoader>
    <div class="flex-center h-360">
        <application-loader></application-loader>
    </div>
</ng-template>

<ng-template #noData>
    <div class="flex-col flex-center h-100-260 min-h-250">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-lead-found' | translate}}</div>
    </div>
</ng-template>
<ng-template #parentLeadDeleted>
    <div class="p-20">
        <h3 *ngIf="isParentLeadDeleted" class="text-center">The associated parent lead has been deleted!</h3>
        <h3 *ngIf="!isParentLeadDeleted && !modalRef?.content?.isPermissionError" class="text-center">You don't have
            sufficient permission to view the lead. Please contact your admin.</h3>
        <h3 *ngIf="!isParentLeadDeleted && modalRef?.content?.isPermissionError" class="text-center">You don't have
            sufficient permission to view this lead. Please contact your admin.</h3>
        <div class="mt-20 flex-end">
            <button type="button" class="btn-green" (click)="modalRef.hide()">OK</button>
        </div>
    </div>
</ng-template>
<ng-template #addFile>
    <h5 class="text-white fw-600 bg-black px-20 py-12">{{ isFilterUpdated ? 'Edit':'Add'}} Filter Name
    </h5>
    <form [formGroup]="FileForm" autocomplete="off" class="pb-20 px-30" (ngSubmit)="onSubmit()">
        <div class="field-label-req">{{ isFilterUpdated ? 'Edit':'Add'}} Filter Name</div>
        <form-errors-wrapper [control]="FileForm.controls['name']" label="filter name">
            <input type="text" formControlName="name" placeholder="enter filter name"
                (keydown.enter)="onSubmit(); $event.preventDefault()" tabindex="1">
            <div class="error-message" *ngIf="doesFileNameExist && FileForm.controls.name.status === 'VALID'">
                name already exists
            </div>
        </form-errors-wrapper>
        <div class="flex-end mt-30">
            <button type="button" class="btn-gray mr-20" id="addFileNameCancel" data-automate-id="addFileNameCancel"
                (click)="modalService.hide()">{{
                'BUTTONS.cancel' | translate }}</button>
            <button type="submit" #focusable class="btn-coal" id="addFileName" data-automate-id="addFileName">
                {{ 'Save' }}
            </button>
        </div>
    </form>
</ng-template>
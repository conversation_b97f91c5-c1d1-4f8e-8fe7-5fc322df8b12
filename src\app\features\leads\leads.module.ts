import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { RouterModule } from '@angular/router';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { DragScrollModule } from 'ngx-drag-scroll';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import {
  LEADS_DECLARATIONS,
  routes,
} from 'src/app/features/leads/leads.routes';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AutoDialerComponent } from './auto-dialer/auto-dialer.component';

@NgModule({
  declarations: [...LEADS_DECLARATIONS, AutoDialerComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule,
    AgGridModule,
    ReactiveFormsModule,
    FormsModule,
    NgxMatIntlTelInputComponent,
    DragScrollModule,
    GoogleMapsModule,
    CKEditorModule,
    LottieModule.forRoot({ player: playerFactory }),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
    }),
  ],
  providers: [
    TranslateService,
    // InViewDirective,
    LeadPreviewComponent,
  ],
})
export class LeadsModule { }

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { MigrationBulkUploadComponent } from 'src/app/shared/components/migration-bulk-upload/migration-bulk-upload.component';
import { CountryCodeComponent } from './country-code/country-code.component';
import { GlobalConfigComponent } from './global-config-root.component';
import { IntegrateComponent } from './global-config.component';
import { IntegrationCardComponent } from './integration-card/integration-card.component';
import { CommonFloorComponent } from './integration/common-floor/common-floor.component';
import { BulkFetchFBLeads } from './integration/facebook/bulk-fetch-fb-leads/bulk-fetch-fb-leads.component';
import { FacebookActionsComponent } from './integration/facebook/facebook-actions/facebook-actions.component';
import { FacebookComponent } from './integration/facebook/facebook.component';
import { FbBulkAgencyComponent } from './integration/facebook/fb-bulk-agency/fb-bulk-agency.component';
import { FbBulkAssignComponent } from './integration/facebook/fb-bulk-assign/fb-bulk-assign.component';
import { FbBulkCountryCodeUpdateComponent } from './integration/facebook/fb-bulk-country-code-update/fb-bulk-country-code-update.component';
import { FbBulkLocationUpdateComponent } from './integration/facebook/fb-bulk-location-update/fb-bulk-location-update.component';
import { FbBulkProjectUpdateComponent } from './integration/facebook/fb-bulk-project-update/fb-bulk-project-update.component';
import { UpdateAssignmentsComponent } from './integration/facebook/update-assignments/update-assignments.component';
import { IntegrationAssignmentV2Component } from './integration/integration-assignment-v2/integration-assignment-v2.component';
import { ThirdPartyIntegrationComponent } from './integration/third-party-integration/third-party-integration.component';
import { ModuleSettingsCardComponent } from './module-settings-card/module-settings-card.component';
import { EmailSettingsComponent } from './settings/communication/email-settings/email-settings.component';
import { AddIvrComponent } from './settings/communication/ivr/add-ivr/add-ivr.component';
import { IvrComponent } from './settings/communication/ivr/ivr.component';
import { AddTemplateComponent } from './settings/communication/sms/add-template/add-template.component';
import { ManageSmsComponent } from './settings/communication/sms/manage-sms/manage-sms.component';
import { SmsSettingsComponent } from './settings/communication/sms/sms-settings/sms-settings.component';
import { AddLeadTemplateComponent } from './settings/communication/templates/lead-templates/add-lead-template/add-lead-template.component';
import { LeadTemplatesComponent } from './settings/communication/templates/lead-templates/lead-templates.component';
import { AddProjectTemplateComponent } from './settings/communication/templates/project-templates/add-project-template/add-project-template.component';
import { ProjectTemplatesComponent } from './settings/communication/templates/project-templates/project-templates.component';
import { AddProjectUnitTemplatesComponent } from './settings/communication/templates/project-unit-templates/add-project-unit-templates/add-project-unit-templates.component';
import { ProjectUnitTemplatesComponent } from './settings/communication/templates/project-unit-templates/project-unit-templates.component';
import { AddPropertyTemplateComponent } from './settings/communication/templates/property-templates/add-property-template/add-property-template.component';
import { PropertyTemplatesComponent } from './settings/communication/templates/property-templates/property-templates.component';
import { TemplatesComponent } from './settings/communication/templates/templates.component';
import { AmenitiesAttributesSettingsComponent } from './settings/module/amenities-attributes-settings/amenities-attributes-settings.component';
import { AttendanceSettingsComponent } from './settings/module/attendance-settings/attendance-settings.component';
import { AutomationSettingsComponent } from './settings/module/automation-settings/automation-settings.component';
import { CustomTagsComponent } from './settings/module/custom-tags-settings/custom-tags-settings.component';
import { DataMigrationSettingsComponent } from './settings/module/data-migration-settings/data-migration-settings.component';
import { DataSettingsComponent } from './settings/module/data-settings/data-settings.component';
import { FormSettingsComponent } from './settings/module/form-settings/form-settings.component';
import { GeneralSettingsComponent } from './settings/module/general-settings/general-settings.component';
import { LeadCustomisationComponent } from './settings/module/lead-customisation/lead-customisation.component';
import { LeadRotationAddGroupComponent } from './settings/module/lead-settings/lead-rotation-add-group/lead-rotation-add-group.component';
import { LeadSettingsComponent } from './settings/module/lead-settings/lead-settings.component';
import { AddConfigComponent } from './settings/module/listing-settings/add-config/add-config.component';
import { ListingSettingsComponent } from './settings/module/listing-settings/listing-settings.component';
import { BulkUpdateComponent } from './settings/module/locality-settings/bulk-update/bulk-update.component';
import { BulkUploadComponent } from './settings/module/locality-settings/bulk-upload/bulk-upload.component';
import { LocalitySettingsComponent } from './settings/module/locality-settings/locality-settings.component';
import { LocationUserAssignmentComponent } from './settings/module/locality-settings/location-user-assignment/location-user-assignment.component';
import { AddCityComponent } from './settings/module/locality-settings/manage-city/add-city/add-city.component';
import { CityActionsComponent } from './settings/module/locality-settings/manage-city/city-actions/city-actions.component';
import { ManageCityComponent } from './settings/module/locality-settings/manage-city/manage-city.component';
import { AddCountryComponent } from './settings/module/locality-settings/manage-country/add-country/add-country.component';
import { CountryActionsComponent } from './settings/module/locality-settings/manage-country/country-actions/country-actions.component';
import { ManageCountryComponent } from './settings/module/locality-settings/manage-country/manage-country.component';
import { AddLocalityComponent } from './settings/module/locality-settings/manage-locality/add-locality/add-locality.component';
import { LocalityActionsComponent } from './settings/module/locality-settings/manage-locality/locality-actions/locality-actions.component';
import { ManageLocalityComponent } from './settings/module/locality-settings/manage-locality/manage-locality.component';
import { AddStateComponent } from './settings/module/locality-settings/manage-state/add-state/add-state.component';
import { ManageStateComponent } from './settings/module/locality-settings/manage-state/manage-state.component';
import { StateActionsComponent } from './settings/module/locality-settings/manage-state/state-actions/state-actions.component';
import { AddZoneComponent } from './settings/module/locality-settings/manage-zone/add-zone/add-zone.component';
import { ManageZoneComponent } from './settings/module/locality-settings/manage-zone/manage-zone.component';
import { ZoneActionsComponent } from './settings/module/locality-settings/manage-zone/zone-actions/zone-actions.component';
import { ManageSourceComponent } from './settings/module/manage-source/manage-source.component';
import { AddAgencyComponent } from './settings/module/marketing-settings/agency-name/add-agency-name/add-agency/add-agency.component';
import { AgencyActionComponent } from './settings/module/marketing-settings/agency-name/agency-action/agency-action/agency-action.component';
import { AgencyAdvanceFilterComponent } from './settings/module/marketing-settings/agency-name/agency-advance-filter/agency-advance-filter/agency-advance-filter.component';
import { AgencyNameComponent } from './settings/module/marketing-settings/agency-name/agency-name.component';
import { AddCampaignComponent } from './settings/module/marketing-settings/campaign-name/add-campaign/add-campaign.component';
import { CampaignActionComponent } from './settings/module/marketing-settings/campaign-name/campaign-action/campaign-action/campaign-action.component';
import { CampaignNameAdvanceFilterComponent } from './settings/module/marketing-settings/campaign-name/campaign-name-advance-filter/campaign-name-advance-filter/campaign-name-advance-filter.component';
import { CampaignNameComponent } from './settings/module/marketing-settings/campaign-name/campaign-name.component';
import { AddChannelPartnerComponent } from './settings/module/marketing-settings/channel-partners/add-channel-partner/add-channel-partner/add-channel-partner.component';
import { ChannelPartnerActionComponent } from './settings/module/marketing-settings/channel-partners/channel-partner-action/channel-partner-action/channel-partner-action.component';
import { ChannelPartnerAdvanceFilterComponent } from './settings/module/marketing-settings/channel-partners/channel-partner-advance-filter/channel-partner-advance-filter/channel-partner-advance-filter.component';
import { ChannelPartnersComponent } from './settings/module/marketing-settings/channel-partners/channel-partners.component';
import { ManageMarketingComponent } from './settings/module/marketing-settings/manage-marketing/manage-marketing.component';
import { MarketingBulkUploadComponent } from './settings/module/marketing-settings/manage-marketing/marketing-bulk-upload/marketing-bulk-upload.component';
import { PropertySettingsComponent } from './settings/module/property-settings/property-settings.component';
import { ConfigurationComponent } from './settings/module/qr-customisation/configuration/configuration.component';
import { CustomizationQRFormComponent } from './settings/module/qr-customisation/customization-qr-form/customization-qr-form.component';
import { ManageQrFormComponent } from './settings/module/qr-customisation/manage-qr-form.component';
import { AddReportAutomationComponent } from './settings/module/report-automation/report-automation-components/add-report-automation/add-report-automation.component';
import { ReportAutomationActionsComponent } from './settings/module/report-automation/report-automation-components/report-automation-actions/report-automation-actions.component';
import { ReportAutomationStatusComponent } from './settings/module/report-automation/report-automation-components/report-automation-status/report-automation-status.component';
import { ReportAutomationComponent } from './settings/module/report-automation/report-automation.component';
import { SecuritySettingsComponent } from './settings/module/security-settings/security-settings.component';

const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: IntegrateComponent, pathMatch: 'full' },
      { path: 'facebook', component: FacebookComponent },
      { path: 'integration', component: ThirdPartyIntegrationComponent },
      { path: 'integrations', component: CommonFloorComponent },
      {
        path: 'templates',
        component: TemplatesComponent,
        children: [
          { path: '', redirectTo: 'lead-templates', pathMatch: 'full' },
          { path: 'lead-templates', component: LeadTemplatesComponent },
          { path: 'property-templates', component: PropertyTemplatesComponent },
        ],
      },
      { path: 'lead-settings', component: LeadSettingsComponent },
      { path: 'email-settings', component: EmailSettingsComponent },
      { path: 'general-settings', component: GeneralSettingsComponent },
      { path: 'attendance-settings', component: AttendanceSettingsComponent },
      { path: 'security-settings', component: SecuritySettingsComponent },
      { path: 'data-settings', component: DataSettingsComponent },
      { path: 'property-project-settings', component: PropertySettingsComponent },
      { path: 'locality-settings', component: LocalitySettingsComponent },
      { path: 'bulk-upload-locality', component: BulkUploadComponent },
      { path: 'sms-settings', component: SmsSettingsComponent },
      { path: 'manage-sms', component: ManageSmsComponent },
      { path: 'add-template', component: AddTemplateComponent },
      { path: 'manage-qr', component: ManageQrFormComponent },
      { path: 'custom-tags', component: CustomTagsComponent },
      { path: 'add-customization-form', component: CustomizationQRFormComponent },
      {
        path: 'edit-customization-form/:id',
        component: CustomizationQRFormComponent,
      },
      // { path: 'lead-customization', component: LeadCustomisationComponent },
      // { path: 'form-settings', component: FormSettingsComponent },
      { path: 'migration', component: DataMigrationSettingsComponent },
      { path: 'leads-migration', component: MigrationBulkUploadComponent },
      { path: 'data-migration', component: MigrationBulkUploadComponent },
      { path: 'amenities-attributes', component: AmenitiesAttributesSettingsComponent },
      { path: 'marketing', component: ManageMarketingComponent },
      {
        path: 'marketing-bulk-upload',
        component: MarketingBulkUploadComponent,
      },
      { path: 'listing-management', component: ListingSettingsComponent },
      { path: 'manage-source', component: ManageSourceComponent },
      { path: 'automation-settings', component: AutomationSettingsComponent },
      { path: 'report-automation', component: ReportAutomationComponent },
    ],
  },
  { path: '', component: IntegrateComponent },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GlobalConfigRoutingModule { }
export const GLOBAL_CONFIG_DECLARATIONS = [
  AddProjectTemplateComponent,
  ProjectTemplatesComponent,
  GlobalConfigComponent,
  IntegrateComponent,
  ThirdPartyIntegrationComponent,
  FacebookComponent,
  IvrComponent,
  AddIvrComponent,
  CommonFloorComponent,
  BulkFetchFBLeads,
  LocalitySettingsComponent,
  AddLocalityComponent,
  AddCityComponent,
  AddZoneComponent,
  CityActionsComponent,
  LocationUserAssignmentComponent,
  ZoneActionsComponent,
  LocalityActionsComponent,
  ManageCityComponent,
  ManageZoneComponent,
  ManageLocalityComponent,
  BulkUpdateComponent,
  BulkUploadComponent,
  SmsSettingsComponent,
  ManageSmsComponent,
  AddTemplateComponent,
  TemplatesComponent,
  LeadSettingsComponent,
  GeneralSettingsComponent,
  LeadTemplatesComponent,
  AddLeadTemplateComponent,
  PropertyTemplatesComponent,
  AddPropertyTemplateComponent,
  DataSettingsComponent,
  PropertySettingsComponent,
  ManageQrFormComponent,
  CustomizationQRFormComponent,
  FbBulkProjectUpdateComponent,
  FbBulkAssignComponent,
  CustomTagsComponent,
  SecuritySettingsComponent,
  LeadRotationAddGroupComponent,
  EmailSettingsComponent,
  AttendanceSettingsComponent,
  DataMigrationSettingsComponent,
  ModuleSettingsCardComponent,
  IntegrationCardComponent,
  AmenitiesAttributesSettingsComponent,
  LeadCustomisationComponent,
  FormSettingsComponent,
  FbBulkAgencyComponent,
  UpdateAssignmentsComponent,
  ProjectUnitTemplatesComponent,
  AddProjectUnitTemplatesComponent,
  FbBulkAgencyComponent,
  ManageMarketingComponent,
  AgencyNameComponent,
  CampaignNameComponent,
  ChannelPartnersComponent,
  AddAgencyComponent,
  AddCampaignComponent,
  AddChannelPartnerComponent,
  AgencyActionComponent,
  ChannelPartnerActionComponent,
  CampaignActionComponent,
  AgencyAdvanceFilterComponent,
  ChannelPartnerAdvanceFilterComponent,
  CampaignNameAdvanceFilterComponent,
  FbBulkLocationUpdateComponent,
  FbBulkCountryCodeUpdateComponent,
  MarketingBulkUploadComponent,
  ListingSettingsComponent,
  AddConfigComponent,
  ReportAutomationComponent,
  ConfigurationComponent,
  ManageStateComponent,
  AddStateComponent,
  StateActionsComponent,
  ManageCountryComponent,
  AddCountryComponent,
  CountryActionsComponent,
  CountryCodeComponent,
  FacebookActionsComponent,
  ManageSourceComponent,
  AutomationSettingsComponent,
  ReportAutomationStatusComponent,
  ReportAutomationActionsComponent,
  AddReportAutomationComponent,
  IntegrationAssignmentV2Component
];
